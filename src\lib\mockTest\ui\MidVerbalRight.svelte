<script>
  import MidRight from './MidRight.svelte';

	import MarkBar from "$lib/mockTest/ui/MarkBar.svelte";
	import Choices from "$lib/mockTest/ui/MultipleChoices.svelte";
	import Question from "./Question.svelte";


    
  /** @type {{i: any, data: any, setMarked?: any, toggleElimination?: any, setCross?: any, setAnswer?: any, studentAnswers: any, studentCross?: any, isMarked?: any, isEliminateTool?: boolean}} */
  let {
    i,
    data,
    setMarked = () => null,
    toggleElimination = () => null,
    setCross = () => null,
    setAnswer = () => null,
    studentAnswers,
    studentCross = null,
    isMarked = [],
    isEliminateTool = false
  } = $props();
</script>

<MidRight>
    <!-- Index, Mark, and Elimination Bar -->
    <MarkBar {i} {isMarked} {setMarked} {isEliminateTool} {toggleElimination} isSPR={false}/>

    <!-- Questions -->
    <Question question={data.questions[i].question} isMath={false} />

    <!-- Choices -->
    <Choices {data} {i} {studentAnswers} {studentCross} {setAnswer} {setCross} {isEliminateTool}/>
</MidRight>