<!-- 
    @component
    ## SimMid
    A component to display the middle section of the simulation test.
-->
<script module lang="ts">
    declare global {
        interface Window {
            MathJax: any;
        }
    }
</script>

<script lang='ts'>
	import Graph from "../ui/Graph.svelte";
	import MarkBar from "../ui/MarkBar.svelte";
	import MultipleChoices from "../ui/MultipleChoices.svelte";
    import AnswerBox from "../ui/AnswerBox.svelte";
    import { AnnotateManager } from "$lib/annotate/annotateManager";
	import { untrack } from "svelte";

    let {
        currentQuestion,
        currentStudentData,
        setAnswer,
        setMarked,
        setCrossed,
        isMath,
        isCalculatorOpen,
        annotateManager = $bindable(),
        currentModuleIndex,
        currentModuleLength,
        currentQuestionIndex
    } = $props();

    let isSPR = $derived(!(currentQuestion.choices));

    // Elimination tool
    let isEliminateTool = $state(false);

    function toggleElimination() {
        isEliminateTool = !isEliminateTool;
    }

    // Resize function
    let isResizing = $state(false);
    let offsetX = $state(0);
    let positionX  = $state("50%");
    let resizeBar: HTMLElement = $state();

    function startResizing(e: MouseEvent) {
        isResizing = true;
        offsetX = e.clientX - resizeBar.offsetLeft;
        document.body.style.userSelect = "none";
        document.body.style.cursor = "col-resize";
    }

    // Annotate
    let passageSection: HTMLElement = $state(null);
    let questionSection: HTMLElement = $state(null);


    function initializeAnnotateManager() {
        annotateManager = Array(currentModuleLength).fill(null).map(() => new AnnotateManager([questionSection]));
    }

    // Update annotate when module change
    $effect(() => {
        currentModuleIndex; 
        untrack(initializeAnnotateManager);
    })

    // Update annotate when question change
    $effect(() => {
        annotateManager[currentQuestionIndex].resetRoot([questionSection]);
    })

    // Latex
    // Render Latex again each time changing question
    let middleSection: HTMLElement = $state();
    $effect(() => {
        if (middleSection) {
            window.MathJax = {
                tex: {
                    inlineMath: [["$", "$"], ["\\(", "\\)"]],
                    displayMath: [["$$", "$$"], ["\\[", "\\]"]]
                },
                svg: { fontCache: "global" }
            };

            let script = document.createElement('script');
            script.src = "https://cdn.jsdelivr.net/npm/mathjax@4/tex-chtml.js";
            document.head.append(script);
        }
    });
</script>

<svelte:document 
    onmousemove={(e) => {
        if (!isResizing) return;
        if (e.clientX < 460 || e.clientX > window.innerWidth - 460) return;
        positionX = `${e.clientX - offsetX}px`;
    }}
    onmouseup={() => {
        if (!isResizing) return;
        isResizing = false;
        document.body.style.userSelect = "";
        document.body.style.cursor = "";
    }}
/>  

<!-- svelte-ignore a11y_no_static_element_interactions -->
{#key currentQuestion}
<div class="middle flex w-full h-full relative gap-[5px] justify-center" bind:this={middleSection}>
    {#if isMath && !isSPR}
        <div class="middle-math max-w-[780px] flex-1 p-10 flex flex-col gap-4 transition-transform duration-200" class:translate-x-[200px]={isCalculatorOpen}>
            {@render right()}
        </div>
    {:else}
        {#if isSPR}
            <div class="left p-10" style:width={positionX}>
                {@render leftSPR()}
            </div>
        {:else}
            <div class="left p-10" style:width={positionX}>
                {@render leftVerbal()}
            </div>
        {/if}

        <div class="right flex-1 p-10 flex flex-col gap-4">
            {@render right()}
        </div>

        <div class="resize-bar w-[5px] h-full bg-[#D9D9D9] absolute cursor-col-resize" style:left={positionX} onmousedown={startResizing} bind:this={resizeBar}>
            <!-- The draggable thumb/handle -->
            <button class="absolute flex h-9 w-5 items-center justify-center rounded-md bg-gray-800 text-white border-white border-[3px] border-solid left-1/2 -translate-x-1/2 top-[20%] !cursor-grab active:!cursor-grabbing" >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    viewBox="0 0 24 24"
                    fill="currentColor"
                >
                    <!-- Left pointing solid triangle -->
                    <path d="M10 6 L4 12 L10 18 Z"></path>
                    <!-- Right pointing solid triangle -->
                    <path d="M14 6 L20 12 L14 18 Z"></path>
                </svg>
            </button>
        </div>
    {/if}
</div>
{/key}

{#snippet leftSPR()}
<div class="spr-text">
    <h1 class="spr-header">Student-produced response directions</h1>
    <ul class="spr-list list-disc ml-[30px]">
        <li>If you find <b>more than one correct answer</b>, enter only one answer.</li>
        <li>You can enter up to 5 characters for a <b>positive</b> answer and up to 6 characters (including the negative sign) for a <b>negative</b> answer.</li>
        <li>If your answer is a <b>fraction</b> that doesn't fit in the provided space, enter the decimal equivalent.</li>
        <li>If your answer is a <b>decimal</b> that doesn't fit in the provided space, enter it by truncating or rounding at the fourth digit.</li>
        <li>If your answer is a <b>mixed number</b> (such as 3 1/2), enter it as an improper fraction (7/2) or its decimal equivalent (3.5).</li>
        <li>Don't enter <b>symbols</b> such as a percent sign, comma, or dollar sign.</li>
    </ul>
    <p class="spr-example">
        Examples
    </p>
    <div class="spr-table-left-wrapper">
        <table class="spr-table-left">
            <tbody>
                <tr class="spr-table-left-row--1">
                    <td>Answer</td>
                    <td>Acceptable ways to enter answer</td>
                    <td>Unacceptable: will NOT receive credit</td>
                </tr>
                <tr class="spr-table-left-row--2">
                    <td>3.5</td>
                    <td class="spr-table-left-chivo">
                        <p>3.5</p>
                        <p>3.50</p>
                        <p>7/2</p>
                    </td>
                    <td class="spr-table-left-chivo">
                        <p>31/2</p>
                        <p>3 1/2</p>
                    </td>
                </tr>
                <tr class="spr-table-left-row--2">
                    <td>2/3</td>
                    <td class="spr-table-left-chivo">
                        <p>2/3</p>
                        <p>.6666</p>
                        <p>.6667</p>
                        <p>0.666</p>
                        <p>0.667</p>
                    </td>
                    <td class="spr-table-left-chivo">
                        <p>0.66</p>
                        <p>.66</p>
                        <p>0.67</p>
                        <p>.67</p>
                    </td>
                </tr>
                <tr>
                    <td>-1/3</td>
                    <td class="spr-table-left-chivo">
                        <p>-1/3</p>
                        <p>-.3333</p>
                        <p>-0.333</p>
                    </td>
                    <td class="spr-table-left-chivo">
                        <p>-.33</p>
                        <p>-0.33</p>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
{/snippet}

{#snippet leftVerbal()}
    {#if currentQuestion.graph}
        <Graph graph={currentQuestion.graph} {isMath}/>
    {/if}
    
    {#if currentQuestion.intro}
        <div class="intro">{@html currentQuestion.intro}</div>
    {/if}

    {#if currentQuestion.passage2}
        <div class="paired-text">Text 1</div>
    {/if}

    <!-- If the question is of type Student's notes -->
    {#if Array.isArray(currentQuestion.passage)}
        <ul class="qn-list passage fiction">
            {#each currentQuestion.passage as point}
                <li>{@html point}</li>            
            {/each}
        </ul>

    <!-- Every other normal questions -->
    {:else}
        <p class="passage {currentQuestion.intro ? 'fiction' : ''}">
            {@html currentQuestion.passage}
        </p>
    {/if}

    <!-- If paired passage -->
    {#if currentQuestion.passage2}
        <div class="paired-text text-2">Text 2</div>
    {/if}

    {#if currentQuestion.passage2}
        <p class="passage">
            {@html currentQuestion.passage2}
        </p>
    {/if}
{/snippet}

{#snippet right()}
    <!-- Index, Mark, and Elimination Bar -->
    <MarkBar {currentStudentData} {setMarked} {isEliminateTool} {toggleElimination} {isSPR}/>

    <!-- Question -->
    <div class="question-text ml-2" bind:this={questionSection}>
        {@html currentQuestion.question}
    </div>

    <!-- Mutiple Choices -->
    {#if isSPR}
        <AnswerBox {currentStudentData} {setAnswer}/>
    {:else}
        <MultipleChoices {currentQuestion} {currentStudentData} {setAnswer} {setCrossed} {isEliminateTool}/>
    {/if}
{/snippet}

<style>
    .left, .right {
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
    }

    .intro {
        width: 100%;
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .paired-text {
        color: #000;
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 156.875% */
        margin-bottom: 6px;
    }

    .text-2 {
        margin-top: 38px;
    }

    .passage {
        max-width: 645px;   
        width: 100%;
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .fiction {
        padding: 10px 0 0 20px;
    }

    .qn-list {
        margin-left: 1.8em;
        padding-left: 0;
        max-width: 645px;   
        color: var(--charcoal, #333);
        width: auto;
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .fiction {
        padding: 10px 0 0 20px;
    }


    .question-text {
        color: var(--charcoal, #333);
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px;
    }


    /* Student produced response */
    .spr-header {
        color: #000;
        font-family: "Merriweather";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 125.5% */
    }

    .spr-text {
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
        display: flex;
        flex-direction: column;
        gap: 8px;
    }

    .spr-example {
        display: flex;
        justify-content: center;
        align-items: center;
        margin: 27px 0 4px 0;
    }

    .spr-table-left-wrapper {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 16px;
    }

    .spr-table-left {
        width: 75%;
        border: 1px solid black;
        border-collapse: collapse;
        text-align: center;
    }

    .spr-table-left tr, .spr-table-left td {
        border: 1px solid black;
        border-collapse: collapse;
    }

    .spr-table-left-row--1 {
        width: 20%;
    }

    .spr-table-left-row--2 {
        width: 40%;
    }

    .spr-table-left td {
        padding: 23px 10px;
    }

    .spr-table-left-chivo {
        color: var(--Charcoal, #333);
        font-family: "Chivo";
        font-size: 14px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 179.286% */
    }
</style>