<!-- 
    @component
    ## SimReview
    A component to display the review section of the simulation test. It shows the list of questions and their status (answered, unanswered, marked, etc.).
    
    ## Props
    - `data`: The data of the test.
    - `isMarked`: An array of boolean values to indicate whether a question is marked or not.
    - `studentAnswers`: An array of student answers.
    - `setQuestion`: A function to set the current question.
-->

<script lang="ts">

    let {
        moduleTitle,
        studentQuestionsData,
        setQuestion
    } = $props();
</script>

<div class="middle py-[50px] flex flex-col items-center w-full">
    <div class="check">Check your Work</div>
    <div class="on">On test day, you won't be able to move on to the next module until time expires.</div>
    <div class="for">For these practice questions, you can click <span class="bold">Submit</span> when you're ready to submit.</div>
    <div class="review-nav">
        <div class="title-and-icon">
            <div class="title">{moduleTitle}</div>
            <div class="icons">
                <div class="unanswered">
                    <div class="unanswered-icon"></div>
                    <div class="unanswered-text">Unanswered</div>
                </div>
                <div class="for-review">
                    <svg xmlns="http://www.w3.org/2000/svg" width="22" height="22" viewBox="0 0 22 22" fill="none">
                        <path d="M14.6663 1.83301H7.33301C5.77467 1.83301 4.58301 3.02467 4.58301 4.58301V19.2497C4.58301 19.433 4.58301 19.5247 4.67467 19.708C4.94967 20.1663 5.49967 20.258 5.95801 20.0747L10.9997 17.1413L16.0413 20.0747C16.2247 20.1663 16.3163 20.1663 16.4997 20.1663C17.0497 20.1663 17.4163 19.7997 17.4163 19.2497V4.58301C17.4163 3.02467 16.2247 1.83301 14.6663 1.83301Z" fill="#FF66C4"/>
                    </svg>
                    <div class="for-review-text">For Review</div>
                </div>
            </div>
        </div>
        <div class="nav-answer-list w-full grid grid-cols-[repeat(10,minmax(0,1fr))] gap-8">
            {#each studentQuestionsData as question, index}
                <div class="answer-box-container">
                    {#if question.marked}
                        <svg class="marked-icon" xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 15 15" fill="none">
                            <path d="M10 1.25H5C3.9375 1.25 3.125 2.0625 3.125 3.125V13.125C3.125 13.25 3.125 13.3125 3.1875 13.4375C3.375 13.75 3.75 13.8125 4.0625 13.6875L7.5 11.6875L10.9375 13.6875C11.0625 13.75 11.125 13.75 11.25 13.75C11.625 13.75 11.875 13.5 11.875 13.125V3.125C11.875 2.0625 11.0625 1.25 10 1.25Z" fill="#FF66C4"/>
                        </svg>
                    {/if}
                    <button class={question.answer !== null ? "answer-box answered-box" : "answer-box unanswered-box"} onclick={() => setQuestion(index)}>{index + 1}</button>    
                </div>
            {/each}
        </div>
    </div>
</div>

<style>
    .middle {
        min-width: 850px;
        max-width: 1000px;
    }

    .check {
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 32px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .on {
        margin-top: 33px;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .for {
        margin-top: 13px;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    .review-nav {
        width: 90%;
        margin-top: 30px;
        display: inline-flex;
        padding: 35px;
        flex-direction: column;
        gap: 40px;
        align-items: center;
        border-radius: 8px;
        background: #FFF;
        box-shadow: 0px 0px 7px 0px rgba(0, 0, 0, 0.25);
    }

    .title-and-icon {
        width: 100%;
        display: flex;
        padding-bottom: 26px;
        justify-content: space-between;
        align-items: flex-end;
        border-bottom: 1px solid #B2B2B2;
    }

    .title {
        color: #000;
        font-family: "Inter";
        font-size: 20px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .icons {
        display: flex;
        align-items: flex-start;
        gap: 17px; 
    }

    .unanswered, .for-review {
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 6px;
    }

    .unanswered-icon {
        width: 22px;
        height: 22px;
        border: 0.75px dashed #000;
    }

    .unanswered-text, .for-review-text {
        color: #000;
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

    span.bold {
        font-weight: 700;
    }


    .answer-box-container {
        position: relative;
    }

    .marked-icon {
        position: absolute;
        right: -7px;
        top: -6.5px;
        z-index: 1;
    }

    .answer-box {
        width: 45px;
        height: 45px;
        flex-shrink: 0;
        color: #000;
        text-align: center;
        font-family: "Inter";
        font-size: 25px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .unanswered-box {
        border: 1px dashed #000;
        color: #000;
    }

    .answered-box {
        text-decoration: underline;
        background-color: #66E2FF;
    }
</style>