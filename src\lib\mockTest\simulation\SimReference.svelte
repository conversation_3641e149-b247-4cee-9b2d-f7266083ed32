<!-- 
    @component
    ## SimRef
    A pop-up window to access the reference sheet in the simulation test.
-->

<script lang="ts">
    let { setReference } = $props();

    let isReferenceCollapsed = $state(false);
    
    function setCollapsed() {
        isReferenceCollapsed = !isReferenceCollapsed;
    }

    // Dragging function
    let referenceWindow: HTMLElement = $state();
    let isDragging = $state(false);

    let offsetX = $state(0);
    let offsetY = $state(0);

    let positionX = $state("20px");
    let positionY = $state("90px");

    function startDragging(e: MouseEvent) {
        isDragging = true;
        offsetX = referenceWindow.offsetWidth - (e.clientX - referenceWindow.offsetLeft);
        offsetY = e.clientY - referenceWindow.offsetTop;
        document.body.style.userSelect = "none";
    }
</script>

<svelte:document 
    onmousemove={(e) => {
        if (!isDragging) return;
        positionX = `${(window.innerWidth - e.clientX) - offsetX}px`;
        positionY = `${e.clientY - offsetY}px`;
    }}
    onmouseup={() => {
        if (!isDragging) return;
        isDragging = false;
        document.body.style.userSelect = "";
    }}
/>

<!-- svelte-ignore a11y_no_static_element_interactions -->
<div class="reference-window fixed h-[550px] w-[1142px] max-w-full mt-1 bg-white border border-[#505050] border-solid flex flex-col z-40" style:width={isReferenceCollapsed ? "1142px" : "454px"} style:top={positionY} style:right={positionX} bind:this={referenceWindow}>
    <div class="reference-topbar bg-black w-full flex justify-between items-center p-4 cursor-grab active:cursor-grabbing" onmousedown={startDragging}>
        <div class="reference-topbar-text">
            <p>Reference Sheet</p>
        </div>
        <div class="rounded-lg p-2 absolute left-1/2 -translate-x-1/2 hover:bg-[gray]" class:bg-[gray]={isDragging}>
            <svg xmlns="http://www.w3.org/2000/svg" width="20" height="14" viewBox="0 0 20 14" fill="none">
                <circle cx="2" cy="2" r="2" fill="white"/>
                <circle cx="2" cy="12" r="2" fill="white"/>
                <circle cx="10" cy="2" r="2" fill="white"/>
                <circle cx="10" cy="12" r="2" fill="white"/>
                <circle cx="18" cy="2" r="2" fill="white"/>
                <circle cx="18" cy="12" r="2" fill="white"/>
            </svg>
        </div>
        <div class="reference-collapse flex gap-4" onmousedown={e => e.stopPropagation()}>
            <button class="reference-collapse-button hover:bg-[gray] rounded-lg p-1" onclick={setCollapsed} aria-label={isReferenceCollapsed ? "Expand reference sheet" : "Collapse reference sheet"}>
                {#if !isReferenceCollapsed}
                    <svg xmlns="http://www.w3.org/2000/svg" width="27" height="27" viewBox="0 0 27 27" fill="none">
                        <path d="M6 5C5.44772 5 5 5.44772 5 6L5 15C5 15.5523 5.44772 16 6 16C6.55228 16 7 15.5523 7 15L7 7L15 7C15.5523 7 16 6.55228 16 6C16 5.44772 15.5523 5 15 5L6 5ZM5.29289 6.70711L11.6569 13.0711L13.0711 11.6569L6.70711 5.29289L5.29289 6.70711Z" fill="white"/>
                        <path d="M22.3633 23.3643C22.9156 23.3643 23.3633 22.9165 23.3633 22.3643L23.3633 13.3643C23.3633 12.812 22.9156 12.3643 22.3633 12.3643C21.811 12.3643 21.3633 12.812 21.3633 13.3643L21.3633 21.3643L13.3633 21.3643C12.811 21.3643 12.3633 21.812 12.3633 22.3643C12.3633 22.9165 12.811 23.3643 13.3633 23.3643L22.3633 23.3643ZM23.0704 21.6572L16.7064 15.2932L15.2922 16.7074L21.6562 23.0714L23.0704 21.6572Z" fill="white"/>
                    </svg>
                {:else}
                    <svg xmlns="http://www.w3.org/2000/svg" width="27" height="27" viewBox="0 0 27 27" fill="none">
                        <path d="M12.364 13.364C12.9162 13.364 13.364 12.9162 13.364 12.364L13.364 3.36396C13.364 2.81168 12.9162 2.36396 12.364 2.36396C11.8117 2.36396 11.364 2.81168 11.364 3.36396V11.364H3.36396C2.81168 11.364 2.36396 11.8117 2.36396 12.364C2.36396 12.9162 2.81168 13.364 3.36396 13.364L12.364 13.364ZM5.29289 6.70711L11.6569 13.0711L13.0711 11.6569L6.70711 5.29289L5.29289 6.70711Z" fill="white"/>
                        <path d="M15.9993 15.0003C15.447 15.0003 14.9993 15.448 14.9993 16.0003L14.9993 25.0003C14.9993 25.5526 15.447 26.0003 15.9993 26.0003C16.5516 26.0003 16.9993 25.5526 16.9993 25.0003V17.0003H24.9993C25.5516 17.0003 25.9993 16.5526 25.9993 16.0003C25.9993 15.448 25.5516 15.0003 24.9993 15.0003L15.9993 15.0003ZM23.0704 21.6572L16.7064 15.2932L15.2922 16.7074L21.6562 23.0714L23.0704 21.6572Z" fill="white"/>
                    </svg>
                {/if}
            </button>
            <button class="reference-close-button hover:bg-[gray] rounded-lg p-1" aria-label="Close reference sheet" onclick={setReference}>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M13.4 12L19.7 5.7C20.1 5.3 20.1 4.7 19.7 4.3C19.3 3.9 18.7 3.9 18.3 4.3L12 10.6L5.7 4.3C5.3 3.9 4.7 3.9 4.3 4.3C3.9 4.7 3.9 5.3 4.3 5.7L10.6 12L4.3 18.3C4.1 18.5 4 18.7 4 19C4 19.6 4.4 20 5 20C5.3 20 5.5 19.9 5.7 19.7L12 13.4L18.3 19.7C18.5 19.9 18.7 20 19 20C19.3 20 19.5 19.9 19.7 19.7C20.1 19.3 20.1 18.7 19.7 18.3L13.4 12Z" fill="white"/>
                </svg>
            </button>
        </div>
    </div>
    <div class="reference-sheet-container">
        <div class="reference-sheet">
            <div class="reference-sheet-shapes">
                <div class="shape">
                    <img src="/reference-sheet/circle.svg" alt="circle">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/rectangle.svg" alt="rectangle">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/triangle.svg" alt="triangle">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/right_triangle.svg" alt="right_triangle">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/special.svg" alt="special">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/box.svg" alt="box">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/cylinder.svg" alt="cylinder">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/sphere.svg" alt="sphere">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/cone.svg" alt="cone">
                </div>
                <div class="shape">
                    <img src="/reference-sheet/pyramid.svg" alt="pyramid">
                </div>
                
            </div>

            <div class="reference-text">
                <p>The number of degrees of arc in a circle is 360.</p>
                <p>The number of radians of arc in a circle is 2π.</p>
                <p>The sum of the measures in degrees of the angles of a triangle is 180.</p>
            </div>
        </div>
    </div>
</div>

<style>
    .reference-topbar-text {
        color: #FFF;
        font-family: "Inter";
        font-size: 17px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .reference-collapse-button, .reference-close-button {
        display: inline-flex;
        align-items: center;
        justify-content: center;
        gap: 10px;
    }

    .reference-sheet-container {
        overflow-y: auto;
        scrollbar-color: #66E2FF#DAF8FF;
        scrollbar-width: thin;
        height: 100%;
    }

    .reference-sheet {
        padding: 36px 40px;
        display: flex;
        flex-direction: column;
        gap: 60px;
    }

    .reference-sheet-shapes {
        display: inline-flex;
        gap: 25px 90px;
        flex-wrap: wrap;
    }

    .shape {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 140px;
    }

    .shape > * {
        object-fit: contain;
    }

    .reference-text {
        color: #000;
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 33.1px; /* 156.875% */
    }

    /* Width */
    ::-webkit-scrollbar {
        width: 8px;
        margin: 4px 0;
    }

    /* Track */
    ::-webkit-scrollbar-track {
        background: #DAF8FF;
        border-radius: 5px;
    }

    /* Handle */
    ::-webkit-scrollbar-thumb {
        background: #66E2FF;
        border-radius: 5px;
    }
</style>