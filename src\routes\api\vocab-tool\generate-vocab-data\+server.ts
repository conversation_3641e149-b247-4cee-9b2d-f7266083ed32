import { json } from "@sveltejs/kit";
import { Type, type GenerateContentConfig } from "@google/genai";
import { supabase } from "$lib/server/supabase";
import { geminiAI } from "$lib/server";
import { redis } from "$lib/server";
import { Ratelimit } from "@upstash/ratelimit";
import type { VocabCard } from "$lib/types/vocab.types";

// API endpoint for generating vocabulary card data using AI
export const POST = async ({ request }) => {
    const { uid, wordList, isQuizletImport } = await request.json();


    if (!isQuizletImport) {
        // Set up rate limiting: 120 requests per hour per user
        const rateLimit = new Ratelimit({
            redis: redis,
            limiter: Ratelimit.slidingWindow(120, "1 h"),
        })
        // Check if user has exceeded rate limit
        const { success, remaining } = await rateLimit.limit(uid + ":generateVocabData");

        // Reject request if rate limit exceeded or word list exceeds remaining quota
        if (!success || wordList.length > remaining) {
            return json({ message: "Exceeded rate limit. Please try again in an hour.", success: false });
        }
    }

    try {
        // Process all words in parallel, generating vocab card data for each
        const vocabCards = await Promise.all(wordList.map((wordData: { word: string, definition: string }) => getSingleWordData(wordData.word, wordData.definition, uid, isQuizletImport)));
        return json({ success: true, vocabCards });
    } catch (error) {
        console.error('Error fetching vocab data:', error);
        return json({ success: false, message: 'An error occurred while fetching data. Please try again.' });
    }
}


// Generates vocabulary card data for a single word using AI
async function getSingleWordData(word: string, definition: string, uid: string, isQuizletImport: boolean) {
    // Set up rate limiting: 120 requests per hour per user
    const rateLimit = new Ratelimit({
        redis: redis,
        limiter: Ratelimit.slidingWindow(120, "1 h"),
    })
    // Check if user has exceeded rate limit
    const { success } = await rateLimit.limit(uid + ":generateVocabData");

    // Reject request if rate limit exceeded or word list exceeds remaining quota
    if (!success) {
        return json({ message: "Exceeded rate limit. Please try again in an hour.", success: false });
    }

    if (!isQuizletImport) {
        // Check if card already exists in database to act as a cache
        const { data: existingCard, error: queryError } = await supabase
            .from('vocabCards')
            .select('partOfSpeech, charge, difficulty, hint1, hint2')
            .eq('word', word.trim())
            .eq('definition', definition.trim())
            .maybeSingle()

        if (existingCard) {
            return { ...existingCard, word, definition };
        }
    }

    // Generate vocab data using AI
    const data = await generateVocabData(word, definition);

    // Parse the AI-generated JSON response into VocabCard object
    const vocabData: VocabCard = { ...JSON.parse(data), word, definition };

    if (!isQuizletImport) {
        // Insert the new card into Supabase database for future reference
        const { data: insertedData, error: insertError } = await supabase
            .from('vocabCards')
            .insert(vocabData)
            .single();

        if (insertError) {
            console.error('Error inserting vocab data:', insertError);
            throw insertError;
        }
    }
    
    return vocabData;
}


// Configuration for Google's Gemini AI model to generate structured vocab card data
const config: GenerateContentConfig = {
    temperature: 0.8, // Moderate creativity for varied but consistent responses
    thinkingConfig: {
        thinkingBudget: 0 // Disable internal reasoning to save tokens
    },
    responseMimeType: 'application/json',
    responseSchema: {
        type: Type.OBJECT,
        required: ["partOfSpeech", "charge", "difficulty", "hint1", "hint2"],
        properties: {
            word: {
                type: Type.STRING,
            },
            partOfSpeech: {
                type: Type.STRING,
            },
            definition: {
                type: Type.STRING,
            },
            charge: {
                type: Type.STRING,
            },
            difficulty: {
                type: Type.NUMBER,
            },
            hint1: {
                type: Type.STRING,
            },
            hint2: {
                type: Type.STRING,
            },
        },
    },
    // System instructions defining how AI should generate vocabulary card content
    systemInstruction: [
        {
            text: `\n
            You are an expert vocabulary tutor, skilled at providing clear definitions and contextual examples to help students prepare for the SAT.\n
            \n
            For each word and definition submitted by the user, return the following fields:\n
            1. part of speech: deduced from the definition (e.g. noun, verb, adjective, adverb)\n
            2. charge: the connotation of the word. It is one of Neutral, Positive, or Negative. Do not use any other connotation.\n
            3. difficulty: an integer from 1 to 4\n
            4. hint1: a 60-word passage that contains the vocabulary word. The passage follow the structure described below. The vocabulary word should be wrapped in \`<b>...</b>\` tags.\n
            5. hint2: a passage of about 80 words with context using the vocabulary word. The vocabulary word should be wrapped in \`<b>...</b>\` tags, and helpful context clues (other words) should be wrapped in \`<mark style="background-color: #fff9c4">...</mark>\` to assist the reader in inferring the meaning.\n
            \n
            Passage Structure:\n
            - Except for human names, all other names (e.g. scientific species names, novel names, poem names, exhibition names, song names, architecture names) are italic. This should be wrapped in '<i>...</i>' at the start and end of the name. Concepts that have normal English names are not italic.\n
            - The passage topic is one of Social Science, Natural Science, or Humanities.\n 
            - Examples include Scientific Research & Studies, Social Science, Economic Analysis, Environmental Science, Human Behavior & Psychology, Literature, and Art Interpretation.\n
            - The passage refers to actual real studies, scientific articles, or books. Do not make these up. Do not state where the study was published.\n
            - The passage is less analytical and argumentative. It is more descriptive and straightforward.\n
            - The passage is sprinkled with context clues (transition words, punctuations, synonyms, paraphrased versions, contrasting words, etc) to help the student figure out the meaning of the word.\n
            - Try not to introduce the word in the 1st sentence, though that is also entirely ok.\n
            - Don't use smart words like "meticulous" or "intricate" just because you can.\n
            \n
            The output should be a JSON object containing the above fields.\n`
        }
    ],
};

// Use Gemini 2.5 Flash model for fast, cost-effective generation
const model = 'gemini-2.5-flash';

// Makes API call to Gemini AI to generate structured vocabulary card data
async function generateVocabData(word: string, definition: string) {
    const response = await geminiAI.models.generateContent({
        model,
        config,
        contents: `Word: ${word}. Definition: ${definition}`
    });
    let data = response.text;
    return data;
}