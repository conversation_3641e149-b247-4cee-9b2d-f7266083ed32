<script module lang="ts">
    declare const katex: any;
</script>

<script lang="ts">
	import { onMount } from "svelte";

    let { 
        currentStudentData,
        setAnswer
    } = $props();

    let studentAnswers = currentStudentData.answer;

    let inputElement: HTMLInputElement = $state();
    let inputValue = $state(studentAnswers ?? '');

    let latexValue = $derived(inputValue.includes('/') ? inputValue.replace('/', '\\over') : inputValue);

    const allowedChars = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9', '.', '-', '/'];
    const allowedKeys = ['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'];

    function checkInput(e: KeyboardEvent) {
        if (allowedKeys.includes(e.key)) {
            return;
        }

        if (!allowedChars.includes(e.key)) {
            e.preventDefault();
        }

        if (inputValue.length && e.key === '-') {
            e.preventDefault();
        }

        if (isNegative && inputValue.length === 6 || !isNegative && inputValue.length === 5) {
            e.preventDefault();
        }

        if (e.key === '/' && (inputValue.length === 0 || inputValue.includes('/') || inputValue[inputValue.length - 1] === '.')) {
            e.preventDefault();
        }

        if (e.key === '.' && inputValue.includes('.')) {
            e.preventDefault();
        }
    }

    let isNegative = $derived(inputValue.startsWith('-'));

    let displayedElement: HTMLSpanElement = $state();

    $effect(() => {
        setAnswer(inputValue ? inputValue : null);
        katex.render(latexValue, displayedElement, {
            throwOnError: false
        });
    })

    onMount(() => {
        inputElement.focus();
    });
</script>

<svelte:head>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.css" integrity="sha384-5TcZemv2l/9On385z///+d7MSYlvIEw9FuZTIdZ14vJLqWphw7e7ZPuOiCHJcFCP" crossorigin="anonymous">
    <script defer src="https://cdn.jsdelivr.net/npm/katex@0.16.22/dist/katex.min.js" integrity="sha384-cMkvdD8LoxVzGF/RPUKAcvmm49FQ0oxwDF3BGKtDXcEc+T1b2N+teh/OJfpU0jr6" crossorigin="anonymous"></script>
</svelte:head>

<!-- Answer box -->
<div class="answer-box h-[57px] flex items-center justify-center border border-[#333333] border-solid rounded-lg px-2" style:width={isNegative ? "120px" : "102px"}>
    <input class="answer-input border-b border-[#333333] border-solid w-full outline-none tracking-wider" style:font-family="monospace" type="text" bind:this={inputElement} bind:value={inputValue} onkeydown={checkInput} class:answer-input--long={studentAnswers && studentAnswers.startsWith('-')}>
</div>

<!-- Answer Preview -->
<div class="answer-preview">
    Answer Preview: <span bind:this={displayedElement}></span>
</div>

<style>
    .answer-input {
        color: #505050;
        font-family: "Chivo";
        font-size: 28px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 89.643% */
    }

    .answer-preview {
        color: #000;
        font-family: "Merriweather";
        font-size: 20px;
        font-style: normal;
        font-weight: 700;
        line-height: 25.1px; /* 125.5% */
    }
</style>