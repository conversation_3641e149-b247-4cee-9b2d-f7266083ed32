import type { Mission } from '../types/mission.types.ts';
import { MissionMetric, MissionPeriod } from '../types/mission.types.ts';

export const MISSION_CATALOG: Mission[] = [
	{
		id: 'daily_questions',
		name: 'Daily Questions',
		description: 'Answer 5 questions',
		metric: MissionMetric.QUESTIONS_ANSWERED,
		target: 5,
		period: MissionPeriod.DAILY,
		reward: 100,
		route: '/study/question-bank',
		buttonText: 'Start Questions'
	},
	{
		id: 'daily_vocab',
		name: 'Daily Vocab Session',
		description: 'Complete 1 vocabulary session',
		metric: MissionMetric.VOCAB_SESSION,
		target: 1,
		period: MissionPeriod.DAILY,
		reward: 50,
		route: '/study/vocab-tool',
		buttonText: 'Start Vocab'
	},
	{
		id: 'morning_login',
		name: 'Morning Study',
		description: 'Log in between 9 AM - 4 PM',
		metric: MissionMetric.MORNING_LOGIN,
		target: 1,
		period: MissionPeriod.DAILY,
		reward: 25,
	},
	{
		id: 'evening_login',
		name: 'Evening Study',
		description: 'Log in between 7 PM - 11 PM',
		metric: MissionMetric.EVENING_LOGIN,
		target: 1,
		period: MissionPeriod.DAILY,
		reward: 25,
	}
] as const;

/**
 * Get a mission by its ID
 */
export function getMissionById(id: string): Mission | undefined {
	return MISSION_CATALOG.find(mission => mission.id === id);
}

/**
 * Get all daily missions
 */
export function getDailyMissions(): Mission[] {
	return MISSION_CATALOG.filter(mission => mission.period === MissionPeriod.DAILY);
}

/**
 * Get all missions for a specific metric
 */
export function getMissionsByMetric(metric: MissionMetric): Mission[] {
	return MISSION_CATALOG.filter(mission => mission.metric === metric);
}

/**
 * Get the route for a mission
 */
export function getMissionRoute(missionId: string): string {
	const mission = getMissionById(missionId);
	return mission?.route ?? '/study';
}

/**
 * Get the button text for a mission
 */
export function getMissionButtonText(missionId: string): string {
	const mission = getMissionById(missionId);
	return mission?.buttonText ?? 'Start Mission';
}
