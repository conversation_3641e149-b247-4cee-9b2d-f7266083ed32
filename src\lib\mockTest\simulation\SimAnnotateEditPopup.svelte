<!-- 
    @component
    ## Annotate
    A component to annotate the text in the simulation test. It allows the user to highlight the text and add notes to it. The user can also delete the annotation if needed.
-->

<script lang="ts">
    import { currentAnnotate, AnnotateManager } from "$lib/annotate/annotateManager";
	import { onMount } from "svelte";

    let { setCurrentComponent } = $props();

    let isCaution = $state(false);


    function setCaution() {
        isCaution = !isCaution;
    }

    function handleClose() {
        setCurrentComponent('');
        $currentAnnotate?.setFocus(null);
    }


    function truncateText(text: string | null): string {
		if (!text) return '';
		if (text.length >= 100) {
			let l = 39,
				r = text.length - 40;
			let ar = [' ', '.', ',', ';'];
			while (!ar.includes(text[l])) l++;
			while (!ar.includes(text[r])) r--;
			text = text.slice(0, l) + '...' + text.slice(r + 1);
		}
		return text;
	}

    let annoComment = $derived($currentAnnotate?.getCommentText() ?? '');

    $effect(() => {
        $currentAnnotate?.setCommentText(annoComment);
    })

    function handleDelete() {
        $currentAnnotate.deleteHighlight();
        handleClose();
    }

    let annotatePopup: HTMLDivElement = $state(null);
    let textArea: HTMLTextAreaElement = $state(null);

    onMount(() => {
        AnnotateManager.excludedList = [annotatePopup];
        textArea?.focus();
    })
</script>

<!-- svelte-ignore a11y_click_events_have_key_events -->
<!-- svelte-ignore a11y_no_static_element_interactions -->

<div class="annotate-popup bg-white z-50 fixed bottom-0 inset-x-0" onclick={(e) => e.stopPropagation()} bind:this={annotatePopup}>
    <div class="annotate-header bg-black text-white flex items-center justify-between py-4 px-16">
        <div class="head-text"><b>View/ Edit:</b> “{truncateText($currentAnnotate?.getSelectedText())}”</div>
        <button class="close-button font-bold flex gap-2" onclick={handleClose}>
            <div>CLOSE</div>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
                <path d="M10.0579 9.00014L14.7829 4.28264C14.9242 4.14141 15.0035 3.94987 15.0035 3.75014C15.0035 3.55041 14.9242 3.35887 14.7829 3.21764C14.6417 3.07641 14.4502 2.99707 14.2504 2.99707C14.0507 2.99707 13.8592 3.07641 13.7179 3.21764L9.00044 7.94264L4.28294 3.21764C4.14171 3.07641 3.95017 2.99707 3.75044 2.99707C3.55072 2.99707 3.35917 3.07641 3.21794 3.21764C3.07671 3.35887 2.99737 3.55041 2.99737 3.75014C2.99737 3.94987 3.07671 4.14141 3.21794 4.28264L7.94294 9.00014L3.21794 13.7176C3.14765 13.7874 3.09185 13.8703 3.05377 13.9617C3.0157 14.0531 2.99609 14.1511 2.99609 14.2501C2.99609 14.3491 3.0157 14.4472 3.05377 14.5386C3.09185 14.63 3.14765 14.7129 3.21794 14.7826C3.28766 14.8529 3.37062 14.9087 3.46201 14.9468C3.5534 14.9849 3.65143 15.0045 3.75044 15.0045C3.84945 15.0045 3.94748 14.9849 4.03888 14.9468C4.13027 14.9087 4.21322 14.8529 4.28294 14.7826L9.00044 10.0576L13.7179 14.7826C13.7877 14.8529 13.8706 14.9087 13.962 14.9468C14.0534 14.9849 14.1514 15.0045 14.2504 15.0045C14.3495 15.0045 14.4475 14.9849 14.5389 14.9468C14.6303 14.9087 14.7132 14.8529 14.7829 14.7826C14.8532 14.7129 14.909 14.63 14.9471 14.5386C14.9852 14.4472 15.0048 14.3491 15.0048 14.2501C15.0048 14.1511 14.9852 14.0531 14.9471 13.9617C14.909 13.8703 14.8532 13.7874 14.7829 13.7176L10.0579 9.00014Z" fill="white"/>
            </svg>
        </button>
    </div>
    <div class="annotate-body h-[280px] flex flex-col px-16 py-3 justify-center gap-4">
        {#if !isCaution}
        <div class="annotate-decoration flex items-center gap-4">
            <div class="font-semibold">Highlight color:</div>
            <div class="color-list flex gap-3">
                <button class="color-choice w-[27px] h-[27px] rounded-full border-2 border-solid" style:background-color={"yellow"} aria-label="Change color to yellow" onclick={() => {}}></button>
            </div>
        </div>
        <textarea class="max-w-[835px] flex-1 p-3 border-2 border-solid border-black rounded-md resize-none" bind:value={annoComment} bind:this={textArea}></textarea>
        <div class="flex items-center gap-4">
            <button class="flex bg-[#66E2FF] px-7 py-3 rounded-full border border-solid font-semibold" onclick={handleClose}>Save</button>
            <button class="flex items-center" onclick={setCaution}>
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                    <path d="M10 18C10.2652 18 10.5196 17.8946 10.7071 17.7071C10.8946 17.5196 11 17.2652 11 17V11C11 10.7348 10.8946 10.4804 10.7071 10.2929C10.5196 10.1054 10.2652 10 10 10C9.73478 10 9.48043 10.1054 9.29289 10.2929C9.10536 10.4804 9 10.7348 9 11V17C9 17.2652 9.10536 17.5196 9.29289 17.7071C9.48043 17.8946 9.73478 18 10 18ZM20 6H16V5C16 4.20435 15.6839 3.44129 15.1213 2.87868C14.5587 2.31607 13.7956 2 13 2H11C10.2044 2 9.44129 2.31607 8.87868 2.87868C8.31607 3.44129 8 4.20435 8 5V6H4C3.73478 6 3.48043 6.10536 3.29289 6.29289C3.10536 6.48043 3 6.73478 3 7C3 7.26522 3.10536 7.51957 3.29289 7.70711C3.48043 7.89464 3.73478 8 4 8H5V19C5 19.7956 5.31607 20.5587 5.87868 21.1213C6.44129 21.6839 7.20435 22 8 22H16C16.7956 22 17.5587 21.6839 18.1213 21.1213C18.6839 20.5587 19 19.7956 19 19V8H20C20.2652 8 20.5196 7.89464 20.7071 7.70711C20.8946 7.51957 21 7.26522 21 7C21 6.73478 20.8946 6.48043 20.7071 6.29289C20.5196 6.10536 20.2652 6 20 6ZM10 5C10 4.73478 10.1054 4.48043 10.2929 4.29289C10.4804 4.10536 10.7348 4 11 4H13C13.2652 4 13.5196 4.10536 13.7071 4.29289C13.8946 4.48043 14 4.73478 14 5V6H10V5ZM17 19C17 19.2652 16.8946 19.5196 16.7071 19.7071C16.5196 19.8946 16.2652 20 16 20H8C7.73478 20 7.48043 19.8946 7.29289 19.7071C7.10536 19.5196 7 19.2652 7 19V8H17V19ZM14 18C14.2652 18 14.5196 17.8946 14.7071 17.7071C14.8946 17.5196 15 17.2652 15 17V11C15 10.7348 14.8946 10.4804 14.7071 10.2929C14.5196 10.1054 14.2652 10 14 10C13.7348 10 13.4804 10.1054 13.2929 10.2929C13.1054 10.4804 13 10.7348 13 11V17C13 17.2652 13.1054 17.5196 13.2929 17.7071C13.4804 17.8946 13.7348 18 14 18Z" fill="#FF66C4"/>
                </svg>
                <div class="text-[#FF66C4] underline font-semibold">Delete</div>
            </button>
        </div>
        {:else}
        <div class="caution-tab flex flex-col gap-9">
            <div class="text-3xl text-center">Are You Sure You Want to Delete This Annotation?</div>
            <div class="text-md text-center">Once you delete this annotation, any notes you've made will no longer be available.</div>
            <div class="flex gap-16 justify-center">
                <button class="text-[#21D5FF] font-semibold hover:underline" onclick={setCaution}>Keep Annotation</button>
                <button class="bg-[#FFC800] font-semibold px-8 py-5 rounded-full border border-solid" onclick={handleDelete}>Delete Annotation</button>
            </div>
        </div>
        {/if}
    </div>
</div>


<style>
    .annotate-popup {
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        line-height: normal;
    }

</style>