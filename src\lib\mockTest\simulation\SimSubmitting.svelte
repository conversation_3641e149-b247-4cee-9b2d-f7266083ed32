<!-- 
    @component
    ## Submitting
    A fullscreen component to display when the test is being submitted.
    
    ## Props
    - `title`: The title of the test.
-->

<script>
	import { H2, P1 } from "$lib/ui";
    let { title, isModuleLoading } = $props();
</script>

<div class="submitting-container fixed inset-0 flex flex-col gap-4 justify-center items-center bg-white">
    <svg class="logo" xmlns="http://www.w3.org/2000/svg" width="198" height="40" viewBox="0 0 198 40" fill="none">
        <path d="M158.138 0.532319V39.4677H148.746V9.31559H148.518L139.811 14.6388V6.5019L149.411 0.532319H158.138Z" fill="url(#paint0_linear_924_1633)"/>
        <path d="M182.353 40C180.224 40 178.183 39.6578 176.232 38.9734C174.28 38.2763 172.543 37.1736 171.023 35.6654C169.502 34.1445 168.304 32.1546 167.429 29.6958C166.555 27.2243 166.124 24.2142 166.137 20.6654C166.149 17.4588 166.542 14.5817 167.315 12.0342C168.088 9.47402 169.191 7.30038 170.623 5.51331C172.068 3.72624 173.792 2.36375 175.794 1.42585C177.81 0.475285 180.059 0 182.543 0C185.268 0 187.67 0.53232 189.749 1.59696C191.84 2.64892 193.513 4.06844 194.768 5.85551C196.023 7.62991 196.764 9.6071 196.992 11.7871H187.734C187.455 10.5577 186.84 9.62611 185.889 8.9924C184.952 8.34601 183.836 8.02281 182.543 8.02281C180.161 8.02281 178.38 9.05577 177.201 11.1217C176.035 13.1876 175.44 15.9506 175.414 19.4106H175.661C176.194 18.2446 176.96 17.2433 177.962 16.4068C178.963 15.5703 180.11 14.9303 181.403 14.4867C182.708 14.0304 184.09 13.8023 185.547 13.8023C187.879 13.8023 189.939 14.3409 191.726 15.4183C193.513 16.4956 194.914 17.9721 195.927 19.8479C196.941 21.711 197.442 23.8466 197.429 26.2548C197.442 28.967 196.808 31.3625 195.528 33.4411C194.248 35.507 192.474 37.1166 190.205 38.27C187.949 39.4233 185.332 40 182.353 40ZM182.296 32.7757C183.45 32.7757 184.483 32.5032 185.395 31.9582C186.308 31.4132 187.024 30.6717 187.543 29.7338C188.063 28.7959 188.317 27.7376 188.304 26.5589C188.317 25.3676 188.063 24.3093 187.543 23.384C187.036 22.4588 186.327 21.7237 185.414 21.1787C184.514 20.6337 183.481 20.3612 182.315 20.3612C181.466 20.3612 180.674 20.5196 179.939 20.8365C179.204 21.1534 178.564 21.597 178.019 22.1673C177.486 22.725 177.068 23.384 176.764 24.1445C176.46 24.8923 176.301 25.7034 176.289 26.5779C176.301 27.7313 176.568 28.7769 177.087 29.7148C177.607 30.6527 178.317 31.4005 179.216 31.9582C180.116 32.5032 181.143 32.7757 182.296 32.7757Z" fill="url(#paint1_linear_924_1633)"/>
        <path d="M13.792 38.5751H0V1.26209H13.7738C17.5755 1.26209 20.8489 2.00908 23.5939 3.50306C26.3511 4.9849 28.4767 7.12262 29.9707 9.91624C31.4647 12.6977 32.2117 16.0258 32.2117 19.9004C32.2117 23.7872 31.4647 27.1274 29.9707 29.921C28.4889 32.7146 26.3693 34.8584 23.6122 36.3524C20.855 37.8342 17.5816 38.5751 13.792 38.5751ZM9.01854 30.8866H13.4458C15.535 30.8866 17.3022 30.5344 18.7476 29.8299C20.2052 29.1133 21.3044 27.9533 22.0453 26.35C22.7984 24.7346 23.1749 22.5847 23.1749 19.9004C23.1749 17.2161 22.7984 15.0784 22.0453 13.4872C21.2922 11.8839 20.1809 10.73 18.7112 10.0256C17.2536 9.30893 15.456 8.95062 13.3183 8.95062H9.01854V30.8866Z" fill="#303030"/>
        <path d="M57.6458 12.4487C57.5243 11.1126 56.9838 10.0741 56.0242 9.33323C55.0768 8.58017 53.7225 8.20363 51.9613 8.20363C50.7953 8.20363 49.8236 8.35546 49.0463 8.65911C48.2689 8.96277 47.6859 9.38181 47.2972 9.91624C46.9085 10.4385 46.7081 11.0398 46.696 11.72C46.6717 12.2787 46.781 12.7706 47.0239 13.1957C47.279 13.6208 47.6434 13.9974 48.1171 14.3253C48.6029 14.6411 49.1859 14.9205 49.8661 15.1634C50.5463 15.4063 51.3115 15.6189 52.1618 15.8011L55.3683 16.5298C57.2146 16.9307 58.8422 17.4651 60.2511 18.1331C61.6722 18.8012 62.8625 19.5967 63.8221 20.5199C64.7938 21.443 65.5286 22.5058 66.0266 23.7082C66.5246 24.9107 66.7797 26.2589 66.7918 27.7529C66.7797 30.1093 66.1845 32.1316 65.0063 33.8199C63.8282 35.5082 62.1338 36.8018 59.9232 37.7006C57.7247 38.5994 55.0708 39.0488 51.9613 39.0488C48.8398 39.0488 46.119 38.5812 43.7991 37.646C41.4792 36.7107 39.6755 35.2896 38.388 33.3827C37.1005 31.4757 36.4385 29.0647 36.4021 26.1496H45.038C45.1109 27.3521 45.4328 28.3541 46.0036 29.1558C46.5745 29.9574 47.3579 30.5647 48.3539 30.9777C49.3621 31.3907 50.5281 31.5972 51.852 31.5972C53.0666 31.5972 54.0991 31.4332 54.9493 31.1052C55.8117 30.7773 56.4736 30.3218 56.9352 29.7388C57.3968 29.1558 57.6336 28.4877 57.6458 27.7347C57.6336 27.0302 57.415 26.429 56.9899 25.931C56.5647 25.4208 55.9089 24.9836 55.0222 24.6192C54.1477 24.2427 53.0302 23.8965 51.6698 23.5807L47.7709 22.6697C44.54 21.9288 41.9954 20.7324 40.137 19.0805C38.2787 17.4165 37.3556 15.1695 37.3677 12.3394C37.3556 10.0316 37.975 8.00929 39.2261 6.27239C40.4771 4.53549 42.208 3.18119 44.4186 2.2095C46.6292 1.2378 49.1495 0.751953 51.9796 0.751953C54.8703 0.751953 57.3785 1.24387 59.5041 2.22771C61.6418 3.19941 63.2998 4.56585 64.478 6.32705C65.6562 8.08824 66.2574 10.1288 66.2817 12.4487H57.6458Z" fill="#303030"/>
        <path d="M78.2654 38.5751H68.5728L81.1623 1.26209H93.1688L105.758 38.5751H96.0656L87.3022 10.6632H87.0107L78.2654 38.5751ZM76.9718 23.8904H97.2317V30.7409H76.9718V23.8904Z" fill="#303030"/>
        <path d="M103.895 8.58624V1.26209H135.433V8.58624H124.119V38.5751H115.228V8.58624H103.895Z" fill="#303030"/>
        <defs>
            <linearGradient id="paint0_linear_924_1633" x1="200.168" y1="-7.82258" x2="155.78" y2="-14.3502" gradientUnits="userSpaceOnUse">
            <stop stop-color="#66E2FF"/>
            <stop offset="1" stop-color="#FF66C4"/>
            </linearGradient>
            <linearGradient id="paint1_linear_924_1633" x1="200.168" y1="-7.82258" x2="155.78" y2="-14.3502" gradientUnits="userSpaceOnUse">
            <stop stop-color="#66E2FF"/>
            <stop offset="1" stop-color="#FF66C4"/>
            </linearGradient>
        </defs>
    </svg>
    <H2>{isModuleLoading ? "Loading" : "Submitting"} {title}</H2>
    <img src="/submitting.gif" alt="submitting.gif" width="128" height="128">
    <P1>Please do not close this tab</P1>
</div>