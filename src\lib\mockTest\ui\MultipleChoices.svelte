<script lang="ts">
    import { P1 } from "$lib/ui";

    let {
        currentQuestion,
        currentStudentData,
        setAnswer,
        setCrossed,
        isEliminateTool
    } = $props();

    const letters = ['A', 'B', 'C', 'D'];
</script>

<div class="multiple-choices flex flex-col gap-4">
    {#each currentQuestion.choices as choice, index}
        <div class="choice-wrapper flex gap-3 items-center">
            <button class="choice-container flex items-center gap-4 hover:bg-[lightgray] w-full px-3 py-2 rounded-lg border border-[#333] border-solid" class:answer-border={currentStudentData.answer === index} class:crossed={currentStudentData.crossed[index]} onclick={() => setAnswer(index)}>
                <div class="border-2 border-solid rounded-full border-[#333] w-7 h-7 flex items-center justify-center">
                    <!-- <span class="answer-letter select-none">{letters[index]}</span> -->
                </div>

                {#if Array.isArray(choice)}
                    <div class="choice-table-wrapper">
                        <table class="table table--choice">
                            <tbody>
                                {#each choice as row }
                                <tr>
                                    {#each row as item }
                                        <td>{@html item}</td>
                                    {/each}
                                </tr>
                            {/each}
                            </tbody>
                        </table>
                    </div>
                {:else}
                    <p class="answer-text select-text text-start">{@html choice}</p>
                {/if}
            </button>

            {#if isEliminateTool}
                {#if currentStudentData.crossed[index]}
                    <button class='undo-cross select-none' onclick={() => setCrossed(index)}><u>Undo</u></button>
                {:else}
                    <button class="cross-choice select-none w-5 h-5 rounded-full border-black border-solid border flex items-center justify-center mx-2" onclick={() => {setCrossed(index)}}>{letters[index]}</button>
                {/if}
            {/if}
        </div>
    {/each}
</div>

<style>
    .cross-choice {
        font-family: 'Inter';
        font-size: 12px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
        position: relative;
    }

    .cross-choice::after {
        content: '';
        position: absolute;
        width: 150%;
        background-color: black;
        height: 1.5px;
    }

    .crossed {
        position: relative;
        display: flex;
        pointer-events: none;
        opacity: 0.6;
    }

    .crossed::after {
        content: '';
        position: absolute;
        width: 102%;
        background-color: black;
        height: 2px;
        left: -1%;
    }

    .undo-cross {
        color: #000;
        font-family: 'Inter';
        font-size: 15px;
        font-style: normal;
        font-weight: 600;
        line-height: 25.1px; /* 167.333% */
        text-decoration-line: underline;
    }

    .answer-border {
        outline: 3px solid #66E2FF;
    }

    .answer-letter {
        color: var(--charcoal, #333);
        font-family: "Inter";
        font-size: 15px;
        font-style: normal;
        font-weight: 600;
        line-height: normal;
    }

    .answer-text {
        color: var(--charcoal, #333);
        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .choice-table-wrapper {
        margin: 12px 0;
    }

    .table {
        width: 100%;
        text-align: center;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table tr {
        width: 50%;
        border: 1px solid black;
        border-collapse: collapse;
    }

    .table td {
        padding: 14px 10px;
        border: 1px solid black;
        border-collapse: collapse;
        color: var(--Charcoal, #333);

        /* p1 Test */
        font-family: "Merriweather";
        font-size: 16px;
        font-style: normal;
        font-weight: 400;
        line-height: 25.1px; /* 156.875% */
    }

    .table--choice td {
        padding: 13px 22px;
    }
</style>