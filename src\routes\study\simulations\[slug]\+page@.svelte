<script lang="ts">
	import { AnnotateEditPopup, Bottom, Calculator, Middle, Reference, Review, Submitting, Top, Breaking, Intro } from '$lib/mockTest/simulation';

	import { onMount } from 'svelte';
	import { addDoc, collection, serverTimestamp, doc, getDoc } from 'firebase/firestore';
	import { db, user } from '$lib/firebase';
	import { error } from '@sveltejs/kit';
	import { finalAnswers, finalMarked } from '$lib/stores';
	import { get } from 'svelte/store';
	import { browser } from "$app/environment";

	interface CorrectAnswer {
		recommended: number[];
		possible: number[];
	}

	interface Question {
		questionType: string;
		topic?: string;
		intro?: string;
		passage: string;
		passage2?: string;
		question: string;
		choices?: number[];
		correctAnswer: number | CorrectAnswer;
		graph?: string;
	}

	interface Module {
		title: string;
		simulation: string;
		module: string;
		questions: Question[];
	}

	interface Data {
		title: string;
		slug: string;
		modules: Module[];
	}

	interface Props {
		data: Data;
	}

	let { data }: Props = $props();

	let tmpStudentCross: boolean[][];

	let currentModuleIndex: number = $state(0);
	let currentQuestionIndex: number = $state(0);

	let currentModule: Module = $derived(data.modules[currentModuleIndex]);

	// Reinitialize student answers and marked status when changing module
	let studentAnswers = $state(Array(data.modules[currentModuleIndex].questions.length).fill(null) as (number | null)[]);
	let studentCross = $state([...Array(data.modules[currentModuleIndex].questions.length)].map((_) => Array(4).fill(false)) as boolean[][]);
	let isMarked = $state(new Array(data.modules[currentModuleIndex].questions.length).fill(false) as boolean[]);

	let submitting = $state(false);

	function setMarked(): void {
		isMarked[currentQuestionIndex] = !isMarked[currentQuestionIndex];
	}

	function setAnswer(answerIndex: number): void {
		studentAnswers[currentQuestionIndex] = answerIndex;
	}

	function setCross(index: number): void {
		if (index === studentAnswers[currentQuestionIndex]) studentAnswers[currentQuestionIndex] = null;
		tmpStudentCross = studentCross.slice();
		tmpStudentCross[currentQuestionIndex][index] = !tmpStudentCross[currentQuestionIndex][index];
		studentCross = tmpStudentCross;
	}

	function setQuestion(questionIndex: number): void {
		currentQuestionIndex = questionIndex;
	}

	function toReview(): void {
		currentQuestionIndex = data.modules[currentModuleIndex].questions.length;
	}

	function nextModule(): void {
		submitting = true;

		// TODO: Save the current module's answers
		if (currentModuleIndex !== 2) {
			// Reset the submitting status
			setTimeout(() => {
				// Increment the module number
				currentModuleIndex += 1;

				// Save the current module's answers
				finalAnswers.update((current) => [...current, studentAnswers]);
    			finalMarked.update((current) => [...current, isMarked]);

				// Reset the student's answers, marked status and timer
				studentAnswers = Array(data.modules[currentModuleIndex].questions.length).fill(null) as (number | null)[];
				studentCross = [...Array(data.modules[currentModuleIndex].questions.length)].map((_) => Array(4).fill(false)) as boolean[][];
				isMarked = new Array(data.modules[currentModuleIndex].questions.length).fill(false) as boolean[];

				now = Date.now();
				end = Date.now() + allotedTime;

				// Reset the question number
				currentQuestionIndex = 0;
				submitting = false;
			}, 1000);
		} else {
			setBreaking();

			// Increment the module number
			currentModuleIndex += 1;
			data.modules[currentModuleIndex] = data.modules[currentModuleIndex];

			// Reset the student's answers, marked status and timer
			studentAnswers = Array(data.modules[currentModuleIndex].questions.length).fill(null) as (number | null)[];
			studentCross = [...Array(data.modules[currentModuleIndex].questions.length)].map((_) => Array(4).fill(false)) as boolean[][];
			isMarked = new Array(data.modules[currentModuleIndex].questions.length).fill(false) as boolean[];

			now = Date.now();
			end = Date.now() + allotedTime;

			// Reset the question number
			currentQuestionIndex = 0;
			submitting = false;
		}
	}


	// FIXME
	async function submit(): Promise<void> {
		// Set to true to change screen
		submitting = true;

		// Save the last module's answers
		finalAnswers.update((current) => [...current, [...studentAnswers]]);
    	finalMarked.update((current) => [...current, [...isMarked]]);

		let finalAns = get(finalAnswers);
		let finalMar = get(finalMarked);

		// Scoring
		let scores: number[] = [0, 0, 0, 0];

		const realModules = data.modules.toSpliced(2, 1);
		for (let i = 0; i < realModules.length; i++) {
			let currentModule = realModules[i];
			for (let j = 0; j < currentModule.questions.length; j++) {
				let currentQuestion = currentModule.questions[j];
				let correctAnswer = currentQuestion.correctAnswer;

				let stuAns = finalAns[i][j];

				if (typeof correctAnswer !== 'object' && correctAnswer === stuAns) {
					scores[i]++;
				} else if (
					typeof correctAnswer === 'object' &&
					stuAns != null &&
					correctAnswer.recommended
						.concat(correctAnswer.possible)
						.some(
							(answer: number) =>
								stuAns == answer ||
								(typeof stuAns === 'string' && stuAns?.includes('/') &&
									parseInt(stuAns.split('/')[0]) / parseInt(stuAns.split('/')[1]) == answer)
						)
				) scores[i]++;
			}
		}

		const VERBAL_SCORES: number[] = [
			200, 200, 200, 200, 200, 210, 230, 240, 250, 260,  // 0-9
			290, 320, 330, 340, 350, 360, 370, 380, 390, 400,  // 10-19
			410, 430, 440, 440, 450, 460, 480, 480, 490, 500,  // 20-29
			500, 510, 520, 530, 550, 560, 570, 580, 590, 600,  // 30-39
			620, 630, 640, 650, 670, 680, 690, 700, 710, 720,  // 40-49
			730, 750, 770, 790, 800                            // 50-54
		];

		const MATH_SCORES: number[] = [
			200, 200, 200, 200, 220, 240, 260, 270, 290, 300,  // 0-9
			320, 330, 350, 360, 380, 390, 410, 420, 440, 450,  // 10-19
			470, 480, 500, 510, 530, 540, 560, 570, 590, 600,  // 20-29
			620, 630, 650, 660, 680, 690, 710, 720, 740, 750,  // 30-39
			760, 770, 780, 790, 800                            // 40-44
		];

		const predictedVerbalScore: number = VERBAL_SCORES[scores[0] + scores[1]];
		const predictedMathScore: number = MATH_SCORES[scores[2] + scores[3]];

		if (browser) {
			const previousVerbalScore = localStorage.getItem('previousVerbalScore');
			const previousMathScore = localStorage.getItem('previousMathScore');

			if (previousVerbalScore && previousMathScore) {
				localStorage.setItem('previousVerbalScore', predictedVerbalScore.toString());
				localStorage.setItem('previousMathScore', predictedMathScore.toString());
			}
		}

		// TODO: Creating a document to store the result
		const docRef = await addDoc(collection(db, 'Simulation'), {
			user: $user.uid,
			simulation: data.slug,
			answers: finalAns.flat(),
			marked: finalMar.flat(),
			rawModuleScores: scores,
			predictedVerbalScore: predictedVerbalScore,
			predictedMathScore: predictedMathScore,
			predictedTotalScore: predictedVerbalScore + predictedMathScore,
			submitTime: serverTimestamp(),
		});

		// Redirect to walkthrough page
		window.location.href = `/study/analysis/${docRef.id}`;
	}



	// Annotate



	// Reference
	let isReferenceOpen = $state(false);

	function openReference(): void {
		isReferenceOpen = !isReferenceOpen;
	}

	// Calculator
	let isCalculatorOpen = $state(false);

	let isCalculatorCollapsed: boolean = true;

	function openCalculator(): void {
		isCalculatorOpen = !isCalculatorOpen;
	}


	let currentComponent = $state("");
	function setBreaking(): void {
        currentComponent = isBreaking ? "" : 'breaking';
    }

	// Set up timer
	const allotedTimes: number[] = [32, 32, 10, 35, 35];


	let now = $state(Date.now());


    // Make it countdown
    const interval = setInterval(() => { if (!isIntroOpen) now = Date.now() }, 100);


	let isIntroOpen = $state(true);
	let introSection = $state(null);

	function closeIntro(): void {
		now = Date.now();
    	end = Date.now() + allotedTime;
		introSection.saveScores();
		isIntroOpen = false;
	}

	// Watch for changes to uid
	let isLoading = $state(true);

	if (browser) {
        loadUserData();
		isLoading = false;
    }

    async function loadUserData(): Promise<void> {
        const hasAimScore = localStorage.getItem('aimScore');

        if (hasAimScore) {
            isIntroOpen = false;
        } else if ($user) {
            try {
                const docRef = doc(db, 'users', $user.uid);
                const docSnap = await getDoc(docRef);

                if (docSnap.exists()) {
                    const userData = docSnap.data();
                    if (userData.aimScore) {
                        localStorage.setItem('aimScore', userData.aimScore);
                        localStorage.setItem('previousVerbalScore', userData.previousVerbalScore);
                        localStorage.setItem('previousMathScore', userData.previousMathScore);
                    }
                }
            } catch (error) {
                console.error("Error fetching user data:", error);
            }
        }
    }


	onMount(async () => {
		// Disable copy/cut
		['copy', 'cut'].forEach((event) =>
			document.addEventListener(event, async () => {
				if (!navigator.clipboard) {
					// Create a "hidden" input
					var aux = document.createElement('input');

					// Assign it the value of the specified element
					aux.setAttribute('value', '');

					document.body.appendChild(aux);

					// Highlight its content
					aux.select();

					// Copy the highlighted text
					document.execCommand('copy');

					document.body.removeChild(aux);
				} else {
					// Use clipboard API in case execCommand is not supported
					await navigator.clipboard.writeText('').catch();
				}
			})
		);
	});
	let isMath = $derived(['Algebra', 'Data Analysis', 'Geometry'].includes(data.modules[currentModuleIndex]?.questions[currentQuestionIndex]?.questionType));

	// $effect(() => {
	// 	if (!isMath) resetHighlightAll([curPassage, curIntro, curPassage_2]);
	// });

	let isSPR = $derived(!(data.modules[currentModuleIndex].questions[currentQuestionIndex]?.choices));
	let isBreaking = $derived(currentComponent === 'breaking');
	let allotedTime = $derived(allotedTimes[currentModuleIndex] * 60 * 1000);
    let end = $derived(Date.now() + allotedTime);

    // Only takes minutes and seconds
    let count = $derived(Math.floor(end - now));
    let minutes = $derived(Math.floor((count % (1000 * 60 * 60)) / (1000 * 60)));
    let seconds = $derived(Math.floor((count % (1000 * 60)) / 1000));
    // Stop countdown and submit when the time hits 0
    $effect(() => {
		if (count <= 0 && !submitting) {
	        if (currentModuleIndex === 4) {
				clearInterval(interval);
				submit();
			} else {
				nextModule();
			}
	    }
	});
</script>

<svelte:head>
	<link
		rel="stylesheet"
		href="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.css"
		integrity="sha384-wcIxkf4k558AjM3Yz3BBFQUbk/zgIYC2R0QpeeYb+TwlBVMrlgLqwRjRtGZiK7ww"
		crossorigin="anonymous"
	/>
	<script
		src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/katex.min.js"
		integrity="sha384-hIoBPJpTUs74ddyc4bFZSM1TVlQDA60VBbJS0oA934VSz82sBx1X7kSx2ATBDIyd"
		crossorigin="anonymous"
	></script>
	<script
		src="https://cdn.jsdelivr.net/npm/katex@0.16.10/dist/contrib/auto-render.min.js"
		integrity="sha384-43gviWU0YVjaDtb/GhzOouOXtZMP/7XUzwPTstBeZFe/+rCMvRwr4yROQP43s0Xk"
		crossorigin="anonymous"
	></script>

	<title>{data.title} - DSAT16</title>
</svelte:head>

<!-- <svelte:window
	onload={() => {
        if (isMath) renderMathInElement(document.body, {
			delimiters: [
				{ left: '$$', right: '$$', display: true },
				{ left: '$', right: '$', display: false }
			],

			throwOnError: false
		})}}
/> -->

{#if currentModuleIndex == 2}
<SimBreaking setBreaking={nextModule} isInModule={false} {minutes} {seconds} />
{:else if !submitting && !isLoading}
	<div class="hide no-overflow">
		<Top
			data={data.modules[currentModuleIndex]}
			{isMath}
			{openReference}
			{openCalculator}
			{currentQuestionIndex}
			{currentComponent}
			{isBreaking}
			{setBreaking}
			{allotedTime}
			{minutes}
			{seconds}
		/>

		{#if isIntroOpen}
			<MinitestIntro uid={$user.uid} bind:this={introSection}/>
		{:else if currentQuestionIndex > data.modules[currentModuleIndex].questions.length - 1}
			<Review data={data.modules[currentModuleIndex]} {isMarked} {studentAnswers} {setQuestion} />
		<!-- Give the student a 10-minute break after verbal -->
        {:else}
			<!-- {#key isCaution} -->
			<Middle
				data={data.modules[currentModuleIndex]}
				{studentAnswers}
				{isMarked}
				{setAnswer}
				{setMarked}
				i={currentQuestionIndex}
				{studentCross}
				{setCross}
				{isMath}
				{isSPR}
				{isCalculatorOpen}
			/>
			<!-- {/key} -->
		{/if}
		<Bottom
			data={data.modules[currentModuleIndex]}
			{isMarked}
			{studentAnswers}
			i={currentQuestionIndex}
			m={currentModuleIndex}
			{nextModule}
			{nextQuestion}
			{setQuestion}
			{previousQuestion}
			{toReview}
			{submit}
			{finalAnswers}
			{finalMarked}
			{closeIntro}
			{isIntroOpen}
		/>

		{#if !isMath}
			<!-- <div class={isAnnotate ? 'hide' : 'show hide'}>
				<Annotate
					{setAnnotate}
					{annoArray}
					{tmpId}
					bind:annoText
					{setContent}
					{handleClose}
					{isAnnotate}
					bind:isCaution
					{handleDelete}
					bind:editText
				/>
			</div> -->
		{/if}

		{#if isReferenceOpen}
			<Reference {openReference} />
		{/if}

		<!-- Don't use if -->
		<div class:show={!isCalculatorOpen}>
			<Calculator {openCalculator} {isCalculatorCollapsed} />
		</div>
	</div>
{:else}
	<Submitting title={currentModuleIndex == 4 ? data.title : data.modules[currentModuleIndex].title} {isLoading} />
{/if}

<div class="show-wrapper">
	<div class="show">
		<div class="title">{data.title}</div>
		<div class="caution">Lưu ý</div>
		<div class="text">
			This feature is not supported on this device. To access {data.title}, please switch to a device with a larger screen.
		</div>
	</div>
</div>

<style>
	.nonselect {
		user-select: none;
	}

	.show {
		display: none;
	}

	.no-overflow {
		overflow: hidden;
		position: sticky;
		height: 100svh;
	}

	@media only screen and (max-width: 960px) {
		.show-wrapper {
			display: flex;
			justify-content: center;
			align-items: center;
			width: 100vw;
			height: 100vh;
		}

		.show {
			width: 75%;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			gap: 14px;
			margin: 64px;
		}

		.title {
			color: #000;
			font-family: 'Inter';
			font-size: 30px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
			text-align: center;
		}

		.caution {
			color: #000;
			font-family: 'Inter';
			font-size: 24px;
			font-style: normal;
			font-weight: 600;
			line-height: normal;
		}

		.text {
			color: #000;
			font-family: 'Open Sans';
			font-size: 16px;
			font-style: normal;
			font-weight: 400;
			line-height: 24px;
			text-align: center;
		}

		.hide {
			display: none;
		}
	}
</style>
